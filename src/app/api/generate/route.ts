import { NextRequest, NextResponse } from "next/server";
import OpenA<PERSON> from "openai";
import { createHenAPIService } from "@/lib/henapi";

// Real API endpoint for generating wedding photos using OpenAI or HenAPI
// Supports multiple AI image generation providers with configurable mode switching

// Get AI generation mode from environment
const AI_GENERATION_MODE = process.env.AI_GENERATION_MODE || "openai";

// Initialize OpenAI client (for openai mode)
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_BASE_URL || "https://api.laozhang.ai/v1"
});

// Initialize HenAPI service (for henapi mode)
const henApiService = createHenAPIService();

export async function POST(request: NextRequest) {
  const requestId = `req-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  try {
    console.log(`🚀 [${requestId}] Starting wedding photo generation API call`);
    console.log(`🔧 [${requestId}] Environment:`, {
      NODE_ENV: process.env.NODE_ENV,
      USE_PLACEHOLDER_IMAGES: process.env.USE_PLACEHOLDER_IMAGES,
      AI_GENERATION_MODE,
      hasOpenAIKey: !!process.env.OPENAI_API_KEY,
      hasHenAPIKey: !!process.env.HENAPI_API_KEY,
      openaiBaseURL: process.env.OPENAI_BASE_URL || "https://api.laozhang.ai/v1",
      henApiBaseURL: process.env.HENAPI_BASE_URL || "https://api.hdgsb.com/v1"
    });

    // Check API configuration based on selected mode
    if (AI_GENERATION_MODE === "henapi") {
      if (!process.env.HENAPI_API_KEY) {
        console.error(`❌ [${requestId}] HENAPI_API_KEY not configured for henapi mode`);
        return NextResponse.json({
          error: "API configuration error: Missing HenAPI API key for henapi mode",
          requestId
        }, { status: 500 });
      }
      if (!henApiService) {
        console.error(`❌ [${requestId}] HenAPI service initialization failed`);
        return NextResponse.json({
          error: "API configuration error: HenAPI service initialization failed",
          requestId
        }, { status: 500 });
      }
    } else {
      // Default to OpenAI mode
      if (!process.env.OPENAI_API_KEY) {
        console.error(`❌ [${requestId}] OPENAI_API_KEY not configured for openai mode`);
        return NextResponse.json({
          error: "API configuration error: Missing OpenAI API key for openai mode",
          requestId
        }, { status: 500 });
      }
    }

    // Development mode check - if API quota is exceeded, use high-quality placeholders
    const isDevelopmentMode = process.env.NODE_ENV === 'development' && process.env.USE_PLACEHOLDER_IMAGES === 'true';
    console.log(`🔧 [${requestId}] Development mode:`, isDevelopmentMode);

    // Parse the request body
    const body = await request.json();
    const { photoUrl, styles, userId } = body;

    console.log(`📝 [${requestId}] Request data:`, {
      photoUrl: photoUrl ? `${photoUrl.substring(0, 50)}...` : "missing",
      styles,
      stylesCount: Array.isArray(styles) ? styles.length : 0,
      userId,
      bodyKeys: Object.keys(body)
    });

    if (!photoUrl) {
      console.error(`❌ [${requestId}] Error: Photo URL is required`);
      return NextResponse.json({
        error: "Photo URL is required",
        requestId
      }, { status: 400 });
    }

    if (!styles || !Array.isArray(styles) || styles.length === 0) {
      console.error(`❌ [${requestId}] Error: At least one style must be selected. Received:`, styles);
      return NextResponse.json({
        error: "At least one style must be selected",
        received: styles,
        requestId
      }, { status: 400 });
    }

    // Validate styles
    const validStyles = [
      "chinese-traditional",
      "western-elegant",
      "beach-sunset",
      "forest-romantic",
      "vintage-classic",
      "modern-chic"
    ];

    const invalidStyles = styles.filter(style => !validStyles.includes(style));
    if (invalidStyles.length > 0) {
      console.error(`❌ [${requestId}] Error: Invalid styles:`, invalidStyles);
      return NextResponse.json({
        error: `Invalid styles: ${invalidStyles.join(", ")}`,
        invalidStyles,
        validStyles,
        requestId
      }, { status: 400 });
    }

    console.log(`✅ [${requestId}] Validation passed, proceeding with generation for ${styles.length} styles`);

    // Validate photo format
    if (!photoUrl.startsWith('data:image/')) {
      console.error(`❌ [${requestId}] Invalid photo format. Expected data URL, got:`, photoUrl.substring(0, 50));
      return NextResponse.json({
        error: "Invalid photo format. Please upload a valid image file.",
        requestId
      }, { status: 400 });
    }

    // Check if the image format is supported by Vision API
    const supportedFormats = ['jpeg', 'jpg', 'png', 'gif', 'webp'];
    const imageFormat = photoUrl.split(';')[0].split('/')[1]?.toLowerCase();

    console.log(`🔍 [${requestId}] Detected image format:`, imageFormat);

    if (!supportedFormats.includes(imageFormat)) {
      console.error(`❌ [${requestId}] Unsupported image format: ${imageFormat}. Supported formats:`, supportedFormats);
      return NextResponse.json({
        error: `Unsupported image format: ${imageFormat}. Please upload a JPEG, PNG, GIF, or WebP image.`,
        supportedFormats,
        requestId
      }, { status: 400 });
    }

    // First, analyze the uploaded photo using OpenAI Vision API
    let photoAnalysis = "";
    let visionApiUsed = false;

    try {
      console.log(`🔍 [${requestId}] Analyzing uploaded photo with Vision API...`);
      console.log(`📷 [${requestId}] Photo details:`, {
        length: photoUrl.length,
        format: imageFormat,
        preview: photoUrl.substring(0, 50) + "..."
      });

      const visionResponse = await openai.chat.completions.create({
        model: "dall-e-3", // Use dall-e-3 model for better quality
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: "Analyze this photo and describe the person's appearance, facial features, hair style, skin tone, and any distinctive characteristics. Focus on details that would help recreate a similar-looking person in wedding photography. Be specific but respectful."
              },
              {
                type: "image_url",
                image_url: {
                  url: photoUrl
                }
              }
            ]
          }
        ],
        max_tokens: 300
      });

      photoAnalysis = visionResponse.choices[0]?.message?.content || "";
      visionApiUsed = true;
      console.log(`✅ [${requestId}] Photo analysis completed successfully`);
      console.log(`📝 [${requestId}] Analysis result:`, photoAnalysis.substring(0, 150) + "...");

    } catch (error) {
      console.error(`⚠️ [${requestId}] Vision API failed, using generic prompts`);
      console.error(`❌ [${requestId}] Vision API error details:`, {
        message: error instanceof Error ? error.message : "Unknown error",
        name: error instanceof Error ? error.name : "Unknown",
        status: (error as any)?.status || "Unknown",
        code: (error as any)?.code || "Unknown",
        type: (error as any)?.type || "Unknown"
      });

      // Provide different fallback descriptions based on image format
      if (imageFormat === 'svg') {
        photoAnalysis = "Beautiful bride with artistic illustration style, elegant features and graceful appearance";
      } else {
        photoAnalysis = "Beautiful bride with elegant features, radiant smile, and graceful appearance";
      }

      console.log(`🔄 [${requestId}] Using fallback description:`, photoAnalysis);
    }

    // Style descriptions for wedding photo prompts - optimized for DALL-E 3
    const stylePrompts: Record<string, string> = {
      "chinese-traditional": "Beautiful bride in elegant traditional Chinese qipao wedding dress, red and gold silk fabric with intricate embroidery, ornate phoenix patterns, classical Chinese garden with pavilion background, bride facing camera with gentle smile, professional wedding photography",
      "western-elegant": "Stunning bride in flowing white wedding gown with lace details, cathedral train, holding white roses bouquet, romantic church interior with stained glass windows, bride facing camera with radiant smile, classic bridal portrait photography",
      "beach-sunset": "Gorgeous bride in flowing bohemian beach wedding dress, soft chiffon fabric, barefoot on sandy beach, golden hour sunset over ocean waves, warm romantic lighting, bride facing camera with joyful expression, destination wedding photography",
      "forest-romantic": "Enchanting bride in romantic A-line wedding dress with floral lace, woodland fairy-tale setting, dappled sunlight through trees, wildflowers and greenery, magical forest atmosphere, bride facing camera with dreamy smile, outdoor wedding photography",
      "vintage-classic": "Elegant bride in vintage 1950s style wedding dress, classic silhouette with pearl details, retro hairstyle with veil, timeless black and white photography aesthetic, bride facing camera with classic pose, vintage wedding portrait",
      "modern-chic": "Sophisticated bride in sleek modern wedding dress, minimalist design with clean lines, contemporary urban setting or modern venue, architectural elements, bride facing camera with confident smile, high-fashion wedding photography"
    };

    const styleNames: Record<string, string> = {
      "chinese-traditional": "Chinese Traditional",
      "western-elegant": "Western Elegant",
      "beach-sunset": "Beach Sunset",
      "forest-romantic": "Forest Romantic",
      "vintage-classic": "Vintage Classic",
      "modern-chic": "Modern Chic"
    };

    // Generate photos using OpenAI API
    console.log(`🎨 [${requestId}] Starting AI image generation for ${styles.length} styles:`, styles);
    const generatedPhotos = [];

    for (let i = 0; i < styles.length; i++) {
      const style = styles[i];
      const stylePrompt = stylePrompts[style];

      console.log(`🖼️ [${requestId}] Generating image ${i + 1}/${styles.length} for style: ${style}`);

      if (!stylePrompt) {
        console.error(`❌ [${requestId}] No prompt found for style: ${style}`);
        continue;
      }

      try {
        let imageUrl: string;

        if (isDevelopmentMode) {
          // Development mode: create high-quality placeholder
          console.log(`🔧 [${requestId}] Development mode: Using placeholder image for style: ${style}`);

          imageUrl = `data:image/svg+xml;base64,${Buffer.from(`
            <svg width="1024" height="1024" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#fdf2f8;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#fce7f3;stop-opacity:1" />
                </linearGradient>
              </defs>
              <rect width="100%" height="100%" fill="url(#bg)"/>
              <circle cx="512" cy="400" r="150" fill="#f9a8d4" opacity="0.3"/>
              <text x="50%" y="30%" text-anchor="middle" dy=".3em" font-family="serif" font-size="36" fill="#be185d" font-weight="bold">
                ${styleNames[style] || style}
              </text>
              <text x="50%" y="40%" text-anchor="middle" dy=".3em" font-family="serif" font-size="24" fill="#9d174d">
                Wedding Photography
              </text>
              <text x="50%" y="70%" text-anchor="middle" dy=".3em" font-family="sans-serif" font-size="16" fill="#831843">
                Development Mode
              </text>
              <text x="50%" y="75%" text-anchor="middle" dy=".3em" font-family="sans-serif" font-size="14" fill="#9f1239">
                High-quality placeholder image
              </text>
            </svg>
          `).toString('base64')}`;

        } else {
          // Production mode: call real API with photo analysis
          const basePrompt = `Professional wedding photography: ${stylePrompt}`;
          const personDescription = photoAnalysis ? `The bride has the following characteristics: ${photoAnalysis}.` : "Beautiful bride with elegant features.";
          const qualityAndPose = "Ultra-high quality, 8K resolution, perfect lighting, romantic and elegant atmosphere. The bride must be facing directly towards the camera, front-facing portrait, beautiful makeup and hairstyle, radiant smile, professional bridal pose. Photorealistic, magazine-quality wedding photography, no text or watermarks.";

          const prompt = `${basePrompt} ${personDescription} ${qualityAndPose}`;

          console.log(`📝 [${requestId}] Using enhanced prompt for ${style}:`, prompt.substring(0, 200) + "...");
          console.log(`🎯 [${requestId}] Photo analysis status:`, {
            visionApiUsed,
            hasAnalysis: !!photoAnalysis,
            analysisLength: photoAnalysis.length,
            imageFormat
          });

          const apiParams = {
            model: "dall-e-3", // Use dall-e-3 model for better quality
            prompt: prompt,
            n: 1,
            size: "1024x1024" as const,
            quality: "standard" as const,
          };

          console.log(`🔄 [${requestId}] Calling ${AI_GENERATION_MODE.toUpperCase()} API with params:`, {
            mode: AI_GENERATION_MODE,
            model: apiParams.model,
            promptLength: apiParams.prompt.length,
            n: apiParams.n,
            size: apiParams.size,
            quality: apiParams.quality
          });

          let apiImageUrl: string;

          if (AI_GENERATION_MODE === "henapi" && henApiService) {
            // Call HenAPI for image generation
            console.log(`🔄 [${requestId}] Using HenAPI for image generation`);
            const henApiParams = {
              model: apiParams.model,
              prompt: apiParams.prompt,
              n: apiParams.n,
              size: apiParams.size
            };

            const henResponse = await henApiService.generateImage(henApiParams);

            console.log(`📥 [${requestId}] HenAPI response for ${style}:`, {
              hasData: !!henResponse.data,
              dataLength: henResponse.data?.length || 0,
              firstImageHasUrl: !!henResponse.data?.[0]?.url
            });

            const henImageUrl = henResponse.data?.[0]?.url;
            if (!henImageUrl) {
              console.error(`❌ [${requestId}] No image URL returned from HenAPI for style: ${style}`);
              console.error(`❌ [${requestId}] Full response:`, henResponse);
              throw new Error("No image URL returned from HenAPI");
            }

            console.log(`✅ [${requestId}] Successfully generated image via HenAPI for style: ${style}, URL length: ${henImageUrl.length}`);
            apiImageUrl = henImageUrl;
          } else {
            // Call OpenAI API for image generation (default mode)
            console.log(`🔄 [${requestId}] Using OpenAI for image generation`);
            const response = await openai.images.generate(apiParams);

            console.log(`📥 [${requestId}] OpenAI API response for ${style}:`, {
              hasData: !!response.data,
              dataLength: response.data?.length || 0,
              firstImageHasUrl: !!response.data?.[0]?.url
            });

            const openaiImageUrl = response.data?.[0]?.url;
            if (!openaiImageUrl) {
              console.error(`❌ [${requestId}] No image URL returned from OpenAI for style: ${style}`);
              console.error(`❌ [${requestId}] Full response:`, response);
              throw new Error("No image URL returned from OpenAI");
            }

            console.log(`✅ [${requestId}] Successfully generated image via OpenAI for style: ${style}, URL length: ${openaiImageUrl.length}`);
            apiImageUrl = openaiImageUrl;
          }

          imageUrl = apiImageUrl;
        }

        generatedPhotos.push({
          id: `photo-${Date.now()}-${i}`,
          style: style,
          styleName: styleNames[style] || style,
          imageUrl: imageUrl,
          originalPhoto: photoUrl,
          createdAt: new Date().toISOString(),
          userId: userId || null,
        });

      } catch (error) {
        console.error(`❌ [${requestId}] Error generating image for style ${style}:`, error);

        // Provide detailed error information
        let userFriendlyMessage = "Image generation failed";

        if (error instanceof Error) {
          console.error(`❌ [${requestId}] Error details for ${style}:`, {
            message: error.message,
            name: error.name,
            stack: error.stack?.substring(0, 500)
          });

          // Handle specific error types
          if (error.message.includes('insufficient_user_quota')) {
            userFriendlyMessage = "API quota exceeded. Please check your API credits.";
          } else if (error.message.includes('404')) {
            userFriendlyMessage = "API endpoint not found. Please check API configuration.";
          } else if (error.message.includes('401') || error.message.includes('403')) {
            userFriendlyMessage = "API authentication failed. Please check your API key.";
          } else if (error.message.includes('rate_limit')) {
            userFriendlyMessage = "API rate limit exceeded. Please try again later.";
          } else if (error.message.includes('content_policy_violation')) {
            userFriendlyMessage = "Content policy violation. Please try a different style.";
          }

          console.error(`❌ [${requestId}] User-friendly message for ${style}: ${userFriendlyMessage}`);
        } else {
          console.error(`❌ [${requestId}] Non-Error object thrown for ${style}:`, error);
        }

        // Create a simple colored placeholder instead of SVG
        const placeholderUrl = `data:image/svg+xml;base64,${Buffer.from(`
          <svg width="400" height="600" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#fef2f2"/>
            <rect x="20" y="20" width="360" height="560" fill="none" stroke="#fca5a5" stroke-width="2" stroke-dasharray="10,5"/>
            <text x="50%" y="40%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="18" fill="#dc2626" font-weight="bold">
              ${styleNames[style] || style}
            </text>
            <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="14" fill="#ef4444">
              Generation Failed
            </text>
            <text x="50%" y="55%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="12" fill="#7f1d1d">
              ${userFriendlyMessage}
            </text>
            <text x="50%" y="70%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="10" fill="#991b1b">
              Please check API configuration
            </text>
          </svg>
        `).toString('base64')}`;

        generatedPhotos.push({
          id: `photo-${Date.now()}-${i}`,
          style: style,
          styleName: styleNames[style] || style,
          imageUrl: placeholderUrl,
          originalPhoto: photoUrl,
          createdAt: new Date().toISOString(),
          userId: userId || null,
          error: userFriendlyMessage
        });
      }
    }

    console.log(`🎉 [${requestId}] Completed generation for all styles. Generated ${generatedPhotos.length} photos`);

    const successfulPhotos = generatedPhotos.filter(photo => !photo.error);
    const failedPhotos = generatedPhotos.filter(photo => photo.error);

    console.log(`📊 [${requestId}] Generation summary:`, {
      total: generatedPhotos.length,
      successful: successfulPhotos.length,
      failed: failedPhotos.length,
      styles: generatedPhotos.map(p => ({ style: p.style, hasError: !!p.error }))
    });

    const responseData = {
      success: true,
      jobId: `job-${Date.now()}`,
      photos: generatedPhotos,
      estimatedTime: "2-3 minutes",
      status: "completed",
      requestId,
      summary: {
        total: generatedPhotos.length,
        successful: successfulPhotos.length,
        failed: failedPhotos.length
      }
    };

    console.log(`📤 [${requestId}] Returning response with ${generatedPhotos.length} generated photos`);

    return NextResponse.json(responseData);

  } catch (error) {
    console.error(`❌ [${requestId}] Error in wedding photo generation API:`, error);
    console.error(`❌ [${requestId}] Error details:`, {
      message: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack?.substring(0, 500) : undefined
    });

    return NextResponse.json({
      error: "Failed to generate wedding photos",
      details: error instanceof Error ? error.message : "Unknown error",
      requestId
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: "Wedding photo generation API",
    currentMode: AI_GENERATION_MODE,
    supportedModes: ["openai", "henapi"],
    configuration: {
      mode: AI_GENERATION_MODE,
      hasOpenAIKey: !!process.env.OPENAI_API_KEY,
      hasHenAPIKey: !!process.env.HENAPI_API_KEY,
      openaiBaseURL: process.env.OPENAI_BASE_URL || "https://api.laozhang.ai/v1",
      henApiBaseURL: process.env.HENAPI_BASE_URL || "https://api.hdgsb.com/v1"
    },
    supportedStyles: [
      "chinese-traditional",
      "western-elegant",
      "beach-sunset",
      "forest-romantic",
      "vintage-classic",
      "modern-chic"
    ]
  });
}
