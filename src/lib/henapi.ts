// HenAPI service for image generation
// Provides integration with https://api.hdgsb.com/v1/images/generations

interface HenAPIGenerationRequest {
  model: string;
  prompt: string;
  n: number;
  size: string;
}

interface HenAPIGenerationResponse {
  data: Array<{
    url: string;
  }>;
}

export class HenAPIService {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL: string = "https://api.hdgsb.com/v1") {
    this.apiKey = apiKey;
    this.baseURL = baseURL;
  }

  async generateImage(params: HenAPIGenerationRequest): Promise<HenAPIGenerationResponse> {
    const headers = new Headers();
    headers.append("Authorization", `Bearer ${this.apiKey}`);
    headers.append("Content-Type", "application/json");

    const requestBody = JSON.stringify({
      model: params.model,
      prompt: params.prompt,
      n: params.n,
      size: params.size
    });

    const requestOptions: RequestInit = {
      method: 'POST',
      headers: headers,
      body: requestBody,
      redirect: 'follow'
    };

    try {
      const response = await fetch(`${this.baseURL}/images/generations`, requestOptions);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HenAPI request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      return result as HenAPIGenerationResponse;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`HenAPI generation failed: ${error.message}`);
      }
      throw new Error('HenAPI generation failed: Unknown error');
    }
  }
}

// Factory function to create HenAPI service instance
export function createHenAPIService(): HenAPIService | null {
  const apiKey = process.env.HENAPI_API_KEY;
  const baseURL = process.env.HENAPI_BASE_URL || "https://api.hdgsb.com/v1";

  if (!apiKey) {
    console.error('HENAPI_API_KEY not configured');
    return null;
  }

  return new HenAPIService(apiKey, baseURL);
}
