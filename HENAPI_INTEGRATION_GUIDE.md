# HenAPI Integration Guide

## 概述

本项目现在支持两种AI图片生成模式：
- **OpenAI模式**: 使用OpenAI的DALL-E 3模型
- **HenAPI模式**: 使用HenAPI的图片生成服务 (https://api.hdgsb.com/v1)

通过环境变量可以轻松切换生成模式，无需修改代码。

## 配置步骤

### 1. 环境变量配置

复制 `.env.example` 到 `.env.local`:
```bash
cp .env.example .env.local
```

### 2. 选择生成模式

#### 使用HenAPI模式 (推荐)
```env
# 图片生成模式选择
AI_GENERATION_MODE=henapi

# HenAPI配置
HENAPI_API_KEY=your_henapi_key_here
HENAPI_BASE_URL=https://api.hdgsb.com/v1

# OpenAI配置 (可选，作为备用)
OPENAI_API_KEY=your_openai_key_here
OPENAI_BASE_URL=https://api.laozhang.ai/v1
```

#### 使用OpenAI模式
```env
# 图片生成模式选择
AI_GENERATION_MODE=openai

# OpenAI配置
OPENAI_API_KEY=your_openai_key_here
OPENAI_BASE_URL=https://api.laozhang.ai/v1

# HenAPI配置 (可选)
HENAPI_API_KEY=your_henapi_key_here
HENAPI_BASE_URL=https://api.hdgsb.com/v1
```

## HenAPI调用方式

### 技术实现

项目使用了以下HenAPI调用方式，参考您提供的代码：

```javascript
var myHeaders = new Headers();
myHeaders.append("Authorization", "Bearer {{YOUR_API_KEY}}");
myHeaders.append("Content-Type", "application/json");

var raw = JSON.stringify({
   "model": "dall-e-3",
   "prompt": "A cute baby sea otter",
   "n": 1,
   "size": "1024x1024"
});

var requestOptions = {
   method: 'POST',
   headers: myHeaders,
   body: raw,
   redirect: 'follow'
};

fetch("https://api.hdgsb.com/v1/images/generations", requestOptions)
   .then(response => response.text())
   .then(result => console.log(result))
   .catch(error => console.log('error', error));
```

### 集成到项目中

1. **HenAPI服务类** (`src/lib/henapi.ts`):
   - 封装了HenAPI的调用逻辑
   - 提供类型安全的接口
   - 统一的错误处理

2. **API路由修改** (`src/app/api/generate/route.ts`):
   - 支持模式切换
   - 保持与现有OpenAI接口的兼容性
   - 详细的日志记录

## 使用流程

1. **配置环境变量**: 设置 `AI_GENERATION_MODE` 和相应的API密钥
2. **启动开发服务器**: `pnpm dev`
3. **测试配置**: 访问 `http://localhost:3000/api/generate` 查看当前配置
4. **生成图片**: 使用现有的婚纱照生成流程

## API响应格式

### GET /api/generate
查看当前配置：
```json
{
  "message": "Wedding photo generation API",
  "currentMode": "henapi",
  "supportedModes": ["openai", "henapi"],
  "configuration": {
    "mode": "henapi",
    "hasOpenAIKey": true,
    "hasHenAPIKey": true,
    "openaiBaseURL": "https://api.laozhang.ai/v1",
    "henApiBaseURL": "https://api.hdgsb.com/v1"
  },
  "supportedStyles": [...]
}
```

### POST /api/generate
生成婚纱照（请求格式保持不变）：
```json
{
  "photoUrl": "data:image/jpeg;base64,...",
  "styles": ["chinese-traditional", "western-elegant"],
  "userId": "optional-user-id"
}
```

## 错误处理

系统会自动处理以下错误情况：
- API密钥缺失或无效
- 网络连接问题
- API配额超限
- 内容策略违规

错误时会生成带有错误信息的占位图片，确保用户体验的连续性。

## 测试验证

运行集成测试：
```bash
node test-henapi-integration.js
```

该脚本会检查：
- 环境配置是否正确
- HenAPI服务文件是否存在
- API路由修改是否完整
- 服务器配置状态

## 优势

1. **灵活切换**: 通过环境变量轻松切换生成模式
2. **向后兼容**: 保持与现有OpenAI集成的完全兼容
3. **统一接口**: 前端代码无需修改
4. **错误恢复**: 完善的错误处理和占位图片
5. **详细日志**: 便于调试和监控

## 注意事项

1. 确保API密钥的安全性，不要提交到版本控制
2. 根据使用量选择合适的API服务商
3. 监控API调用频率和配额使用情况
4. 定期测试两种模式的可用性
