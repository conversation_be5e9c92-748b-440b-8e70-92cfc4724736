#!/usr/bin/env node

/**
 * Demo script showing how to use the new HenAPI integration
 * This script demonstrates the configuration and usage patterns
 */

console.log("🎨 HenAPI Integration Demo");
console.log("=" .repeat(50));

// Demo 1: Environment Configuration Examples
console.log("\n1. 📋 Environment Configuration Examples");
console.log("-" .repeat(30));

console.log("\n🔧 For HenAPI Mode (.env.local):");
console.log(`AI_GENERATION_MODE=henapi
HENAPI_API_KEY=your_henapi_key_here
HENAPI_BASE_URL=https://api.hdgsb.com/v1

# Optional: Keep OpenAI as backup
OPENAI_API_KEY=your_openai_key_here
OPENAI_BASE_URL=https://api.laozhang.ai/v1`);

console.log("\n🔧 For OpenAI Mode (.env.local):");
console.log(`AI_GENERATION_MODE=openai
OPENAI_API_KEY=your_openai_key_here
OPENAI_BASE_URL=https://api.laozhang.ai/v1

# Optional: Keep HenAPI as backup
HENAPI_API_KEY=your_henapi_key_here
HENAPI_BASE_URL=https://api.hdgsb.com/v1`);

// Demo 2: API Usage Examples
console.log("\n\n2. 🚀 API Usage Examples");
console.log("-" .repeat(30));

console.log("\n📡 Check Current Configuration:");
console.log(`curl http://localhost:3000/api/generate`);

console.log("\n📡 Generate Wedding Photos:");
const exampleRequest = {
  photoUrl: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...", // Base64 encoded image
  styles: ["chinese-traditional", "western-elegant"],
  userId: "demo-user-123"
};

console.log(`curl -X POST http://localhost:3000/api/generate \\
  -H "Content-Type: application/json" \\
  -d '${JSON.stringify(exampleRequest, null, 2)}'`);

// Demo 3: Code Integration Examples
console.log("\n\n3. 💻 Code Integration Examples");
console.log("-" .repeat(30));

console.log("\n🔍 Frontend JavaScript Example:");
console.log(`// Check API configuration
async function checkApiConfig() {
  const response = await fetch('/api/generate');
  const config = await response.json();
  console.log('Current mode:', config.currentMode);
  console.log('Available modes:', config.supportedModes);
  return config;
}

// Generate wedding photos
async function generateWeddingPhotos(photoFile, selectedStyles) {
  // Convert file to base64
  const base64 = await fileToBase64(photoFile);
  
  const response = await fetch('/api/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      photoUrl: base64,
      styles: selectedStyles,
      userId: getCurrentUserId()
    })
  });
  
  const result = await response.json();
  return result.photos;
}`);

console.log("\n🔍 Backend Service Example:");
console.log(`// Using HenAPI service directly
import { createHenAPIService } from '@/lib/henapi';

const henApi = createHenAPIService();
if (henApi) {
  const result = await henApi.generateImage({
    model: "dall-e-3",
    prompt: "Beautiful bride in traditional Chinese wedding dress",
    n: 1,
    size: "1024x1024"
  });
  console.log('Generated image URL:', result.data[0].url);
}`);

// Demo 4: Testing and Debugging
console.log("\n\n4. 🧪 Testing and Debugging");
console.log("-" .repeat(30));

console.log("\n🔍 Run Integration Tests:");
console.log(`node test-henapi-integration.js`);

console.log("\n🔍 Check Server Logs:");
console.log(`# Start development server with detailed logs
pnpm dev

# Look for these log patterns:
# 🔧 Environment: { AI_GENERATION_MODE: 'henapi', ... }
# 🔄 Using HenAPI for image generation
# ✅ Successfully generated image via HenAPI`);

console.log("\n🔍 Debug API Calls:");
console.log(`# Enable debug mode in browser console
localStorage.setItem('debug', 'true');

# Check network tab for API calls to:
# - /api/generate (GET) - Configuration check
# - /api/generate (POST) - Image generation`);

// Demo 5: Migration Guide
console.log("\n\n5. 🔄 Migration Guide");
console.log("-" .repeat(30));

console.log("\n📋 Step-by-step migration:");
console.log(`1. Update environment variables:
   - Add AI_GENERATION_MODE=henapi
   - Add HENAPI_API_KEY=your_key
   - Keep existing OPENAI_* variables as backup

2. Test the configuration:
   - Run: node test-henapi-integration.js
   - Start server: pnpm dev
   - Check: curl http://localhost:3000/api/generate

3. Verify image generation:
   - Upload a test photo
   - Select wedding styles
   - Check generated images

4. Monitor and optimize:
   - Check API usage and costs
   - Monitor generation quality
   - Switch modes if needed`);

// Demo 6: Troubleshooting
console.log("\n\n6. 🔧 Troubleshooting");
console.log("-" .repeat(30));

console.log("\n❌ Common Issues and Solutions:");
console.log(`
Issue: "HENAPI_API_KEY not configured"
Solution: Add HENAPI_API_KEY to .env.local

Issue: "HenAPI service initialization failed"
Solution: Check HENAPI_BASE_URL format and network connectivity

Issue: "No image URL returned from HenAPI"
Solution: Verify API key permissions and quota

Issue: Images not generating
Solution: Check logs for specific error messages and API responses

Issue: Mode switching not working
Solution: Restart server after changing AI_GENERATION_MODE
`);

console.log("\n" + "=" .repeat(50));
console.log("🎉 Demo Complete!");
console.log("\n💡 Quick Start:");
console.log("1. cp .env.example .env.local");
console.log("2. Edit .env.local with your API keys");
console.log("3. Set AI_GENERATION_MODE=henapi");
console.log("4. pnpm dev");
console.log("5. Test at http://localhost:3000");

console.log("\n📚 Documentation:");
console.log("- Read: HENAPI_INTEGRATION_GUIDE.md");
console.log("- Test: node test-henapi-integration.js");
console.log("- Debug: Check browser console and server logs");
